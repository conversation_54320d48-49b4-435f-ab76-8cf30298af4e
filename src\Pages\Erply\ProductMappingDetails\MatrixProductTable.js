import React, { useState } from "react";
import { styled } from "@mui/material/styles";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  IconButton,
  Skeleton,
  TableFooter,
  TablePagination,
  useTheme,
  tableCellClasses,
  Chip,
  Tooltip,
  Snackbar,
} from "@mui/material";
import {
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
  UnfoldMore,
  ExpandMore,
  ExpandLess,
  ContentCopy,
} from "@mui/icons-material";
import MuiAlert from "@mui/material/Alert";
import PropTypes from "prop-types";

const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

// Styled components
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.light,
    fontWeight: 600,
    fontSize: "0.875rem",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
  "& svg": {
    position: "relative",
    top: "5px",
    cursor: "pointer",
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  "&:hover": {
    backgroundColor: theme.palette.action.selected,
    cursor: "pointer",
  },
}));

const ParentRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#f5f5f5 !important",
  fontWeight: "600",
}));

const ChildRow = styled(StyledTableRow)(({ theme }) => ({
  backgroundColor: "#fafafa !important",
  "& td": {
    // paddingLeft: "40px",
    fontSize: "13px",
  },
}));

const NoDataContainer = styled(TableRow)({
  position: "relative",
  height: "50px",
});

const NoDataCell = styled(TableCell)({
  position: "absolute",
  right: "50%",
  borderBottom: "none",
  textAlign: "center",
  color: "#666",
  fontStyle: "italic",
});

const CopyableCell = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: "4px",
  cursor: "pointer",
  padding: "2px 4px",
  borderRadius: "4px",
  transition: "background-color 0.2s",
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
  },
  "& .copy-icon": {
    opacity: 0,
    fontSize: "14px",
    transition: "opacity 0.2s",
  },
  "&:hover .copy-icon": {
    opacity: 1,
  },
}));

// Table pagination actions component
function TablePaginationActions({ count, page, rowsPerPage, onPageChange }) {
  const theme = useTheme();

  const handleBackButtonClick = (event) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event) => {
    onPageChange(event, page + 1);
  };

  return (
    <div style={{ flexShrink: "0" }}>
      <IconButton
        onClick={handleBackButtonClick}
        disabled={page === 1}
        aria-label="previous page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowRight />
        ) : (
          <KeyboardArrowLeft />
        )}
      </IconButton>
      <IconButton
        onClick={handleNextButtonClick}
        disabled={page >= Math.ceil(count / rowsPerPage)}
        aria-label="next page"
      >
        {theme.direction === "rtl" ? (
          <KeyboardArrowLeft />
        ) : (
          <KeyboardArrowRight />
        )}
      </IconButton>
    </div>
  );
}

TablePaginationActions.propTypes = {
  count: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  page: PropTypes.number.isRequired,
  rowsPerPage: PropTypes.number.isRequired,
};

// Helper function to check if a row is mapped to Shopify
const isRowMapped = (row, isChild = false) => {
  if (isChild) {
    // For child variants, check multiple possible mapping indicators
    return (
      (row.shopifyVariantID && row.shopifyVariantID !== "") ||
      (row.shopify_product && row.shopify_product.shopifyVariantId && row.shopify_product.shopifyVariantId !== "")
    );
  } else {
    // For parent matrix products, check if they have shopifyProductID
    return row.shopifyProductID && row.shopifyProductID !== "";
  }
};

// Helper function to get Shopify product data
const getShopifyData = (row, isChild = false, field) => {
  const isMapped = isRowMapped(row, isChild);
  if (!isMapped) {
    return "-";
  }

  const shopifyProduct = row.shopify_product;

  if (isChild) {
    // For child variants - access shopify_product data
    if (field === "shopify_products_string_id") {
      return shopifyProduct?.shopify_products_string_id || "-";
    }
    return shopifyProduct?.[field] || "-";
  } else {
    // For parent matrix products - access shopify_product data
    if (field === "shopify_products_string_id") {
      return shopifyProduct?.shopify_products_string_id || row.shopifyProductID || "-";
    }
    return shopifyProduct?.[field] || "-";
  }
};

// Helper function to check for validation mismatches
const hasValidationMismatch = (row, isChild = false, validationType) => {
  if (!isChild) return false; // Only check variants

  const isMapped = isRowMapped(row, true);
  if (!isMapped) return false; // Only check mapped variants

  switch (validationType) {
    case 'sku':
      const erplySku = row.code || "";
      const shopifySku = row.shopify_product?.sku || "";
      return erplySku && shopifySku && erplySku !== shopifySku;

    case 'barcode':
      const erplyBarcode = row.code2 || "";
      const shopifyBarcode = row.shopify_product?.barcode || "";
      return erplyBarcode && shopifyBarcode && erplyBarcode !== shopifyBarcode;

    case 'price':
      const erplyPrice = parseFloat(row.priceWithVat || 0);
      const shopifyPrice = parseFloat(row.shopify_product?.price || 0);
      // Consider prices different if they differ by more than 0.01 (to handle floating point precision)
      return erplyPrice > 0 && shopifyPrice > 0 && Math.abs(erplyPrice - shopifyPrice) > 0.01;

    default:
      return false;
  }
};

// Helper function to copy text to clipboard
const copyToClipboard = async (text, setCopySnackbar) => {
  try {
    await navigator.clipboard.writeText(text);
    setCopySnackbar({
      open: true,
      message: `Copied "${text}" to clipboard`,
      severity: "success"
    });
  } catch (err) {
    console.error('Failed to copy: ', err);
    setCopySnackbar({
      open: true,
      message: "Failed to copy to clipboard",
      severity: "error"
    });
  }
};

// Helper function to render copyable cell content
const renderCopyableCell = (value, setCopySnackbar) => {
  if (!value || value === "-") {
    return value;
  }

  return (
    <Tooltip title="Click to copy">
      <CopyableCell onClick={() => copyToClipboard(value, setCopySnackbar)}>
        <span>{value}</span>
        <ContentCopy className="copy-icon" />
      </CopyableCell>
    </Tooltip>
  );
};

// Cell content renderer
const renderCellContent = (row, column, isChild = false, setCopySnackbar) => {
  const value = row[column.id];

  // Check if this is a copyable column
  const copyableColumns = ['erplySku', 'erplyBarcode', 'shopifySku', 'shopifyBarcode', 'productID', 'code', 'shopifyProductId'];
  const isCopyable = copyableColumns.includes(column.id);

  switch (column.id) {
    case "expand":
      if (isChild) return null;
      return row.type === "MATRIX" && row.erply_product_variants?.length > 0 ? (
        <IconButton size="small">
          <ExpandMore />
        </IconButton>
      ) : null;

    case "priceWithVat":
      return value ? `$${parseFloat(value).toFixed(2)}` : "-";

    case "status":
      return value || "-";

    case "type":
      return (
        <Chip
          label={value || "UNKNOWN"}
          color={value === "MATRIX" ? "primary" : "secondary"}
          size="small"
        />
      );

    case "shopifyProductId":
      const shopifyProductIdValue = getShopifyData(row, isChild, "shopify_products_string_id");
      return isCopyable && shopifyProductIdValue !== "-" ? renderCopyableCell(shopifyProductIdValue, setCopySnackbar) : shopifyProductIdValue;

    case "shopifyTitle":
      return getShopifyData(row, isChild, "title");

    case "shopifyHandle":
      return getShopifyData(row, isChild, "handle");

    case "shopifyStatus":
      return getShopifyData(row, isChild, "status");

    case "erplyBarcode":
      if (isChild) {
        // For child variants, show Erply barcode (code2)
        const barcodeValue = row.code2 || "-";
        return isCopyable && barcodeValue !== "-" ? renderCopyableCell(barcodeValue, setCopySnackbar) : barcodeValue;
      } else {
        // For parent matrix products, don't show barcode
        return "-";
      }

    case "shopifyBarcode":
      if (isChild) {
        // For child variants, show Shopify barcode
        const barcodeValue = row.shopify_product?.barcode || "-";
        return isCopyable && barcodeValue !== "-" ? renderCopyableCell(barcodeValue, setCopySnackbar) : barcodeValue;
      } else {
        // For parent matrix products, don't show barcode
        return "-";
      }

    case "erplySku":
      if (isChild) {
        // For child variants, show Erply SKU (could be code or another field)
        const skuValue = row.code || "-";
        return isCopyable && skuValue !== "-" ? renderCopyableCell(skuValue, setCopySnackbar) : skuValue;
      } else {
        // For parent matrix products, don't show SKU
        return "-";
      }

    case "shopifySku":
      if (isChild) {
        // For child variants, show Shopify SKU
        const skuValue = row.shopify_product?.sku || "-";
        return isCopyable && skuValue !== "-" ? renderCopyableCell(skuValue, setCopySnackbar) : skuValue;
      } else {
        // For parent matrix products, don't show SKU
        return "-";
      }

      case "shopifyColorSize":
      if (isChild) {
        // For child variants, show Shopify color and size
        const size = row?.shopify_product?.size || "";
        const color = row?.shopify_product?.color || "";

        if (!color && !size) {  
          return "-";
        }

        // Build the display string, filtering out empty values
        const parts = [size, color].filter(part => part && part.trim() !== "");
        return parts.length > 0 ? parts.join(" / ") : "-";
      } else {
        // For parent matrix products, don't show color and size
        return "-";
      }

    case "erplyColorSize":
      if (isChild) {
        // For child variants, show Erply color and size from variationDescription
        try {
          if (row.variationDescription) {
            const variations = JSON.parse(row.variationDescription);
            const variationText = variations.map(v => v.value || "").filter(val => val.trim() !== "").join(" / ");
            return variationText || "-";
          }
          return "-";
        } catch (e) {
          return "-";
        }
      } else {
        // For parent matrix products, don't show color and size
        return "-";
      }
    case "name":
      if (isChild && row.variationDescription) {
        try {
          const variations = JSON.parse(row.variationDescription);
          const variationText = variations.map(v => `${v.name}: ${v.value}`).join(", ");
          return (
            <Box>
              <div style={{ fontWeight: "500" }}>{value}</div>
              <div style={{ fontSize: "12px", color: "#666" }}>{variationText}</div>
            </Box>
          );
        } catch (e) {
          return value || "-";
        }
      }
      return value || "-";

    case "productID":
      const productIdValue = value || "-";
      return isCopyable && productIdValue !== "-" ? renderCopyableCell(productIdValue, setCopySnackbar) : productIdValue;

    case "code":
      const codeValue = value || "-";
      return isCopyable && codeValue !== "-" ? renderCopyableCell(codeValue, setCopySnackbar) : codeValue;

    default:
      return value || "-";
  }
};

// Main table component
const MatrixProductTable = ({
  matrixColumns = [],
  variantColumns = [],
  rows = [],
  loading = false,
  sortable = true,
  currentColumn = "",
  direction = false,
  page = 1,
  total = 0,
  fromTable = 0,
  toTable = 0,
  rowsPerPage = 20,
  onSort,
  onChangePage,
  onChangeRowsPerPage,
  expandedRows = new Set(),
  setExpandedRows,
  showChildrenAlso = false,
  validationTypes = [],
}) => {
  // State for copy snackbar
  const [copySnackbar, setCopySnackbar] = useState({
    open: false,
    message: "",
    severity: "success"
  });

  const handleRowExpand = (productID) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(productID)) {
      newExpandedRows.delete(productID);
    } else {
      newExpandedRows.add(productID);
    }
    setExpandedRows(newExpandedRows);
  };

  const handleCopySnackbarClose = () => {
    setCopySnackbar(prev => ({ ...prev, open: false }));
  };

  // Matrix and variant columns are now passed as props

  // Render main table header for matrix products
  const renderMatrixTableHeader = () => (
    <TableHead>
      <TableRow>
        {matrixColumns.map((column) => (
          <StyledTableCell
            key={column.id}
            onClick={sortable && onSort && !["type", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) ? () => onSort(column.id) : undefined}
            style={{
              cursor: sortable && onSort && !["type", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) ? "pointer" : "default",
              borderLeft: column.id === "shopifyProductId" ? "3px solid #8c8c8c" : "none"
            }}
          >
            {column.name}
            {sortable && onSort && !["type", "shopifyProductId", "shopifyTitle", "shopifyHandle", "shopifyStatus"].includes(column.id) && (
              <>
                {currentColumn === column.id ? (
                  direction ? (
                    <KeyboardArrowUp fontSize="small" />
                  ) : (
                    <KeyboardArrowDown fontSize="small" />
                  )
                ) : (
                  <UnfoldMore fontSize="small" />
                )}
              </>
            )}
          </StyledTableCell>
        ))}
      </TableRow>
    </TableHead>
  );

  // Render variants header row (as a table row, not TableHead)
  const renderVariantsHeaderRow = () => (
    <TableRow style={{ backgroundColor: "#3f3681" }}>
      {variantColumns.map((column) => (
        <StyledTableCell
          key={`variant-header-${column.id}`}
          style={{
            fontWeight: "600",
            fontSize: "13px",
            // backgroundColor: "#e8f5e8",
            color: "#ffffff",
            borderLeft: column.id === "shopifySku" ? "3px solid #8c8c8c" : "none",
            padding: "8px 16px"
          }}
        >
          {column.name}
        </StyledTableCell>
      ))}
    </TableRow>
  );

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <TableBody>
      {Array.from({ length: 7 }).map((_, rowIndex) => (
        <TableRow key={rowIndex}>
          {matrixColumns.map((column) => (
            <StyledTableCell key={column.id}>
              <Skeleton variant="text" height={40} />
            </StyledTableCell>
          ))}
        </TableRow>
      ))}
    </TableBody>
  );

  return (
    <Paper sx={{ width: "100%", overflow: "hidden", marginBottom: "40px" }}>
      <TableContainer sx={{ maxHeight: 678 }}>
        <Table stickyHeader aria-label="matrix products table" sx={{ minWidth: 700 }}>
          {renderMatrixTableHeader()}
          {loading ? (
            renderLoadingSkeleton()
          ) : (
            <TableBody>
              {rows.length > 0 ? (
                rows.map((matrixProduct) => {
                  const isExpanded = expandedRows.has(matrixProduct.productID);
                  const hasVariants = matrixProduct.erply_product_variants && matrixProduct.erply_product_variants.length > 0;

                  return (
                    <React.Fragment key={`matrix-${matrixProduct.productID}`}>
                      {/* Matrix Product Row */}
                      <ParentRow
                        sx={{
                          backgroundColor: (() => {
                            const isMapped = isRowMapped(matrixProduct, false);
                            if (isMapped) {
                              return "#d4edda !important";
                            }
                            return undefined;
                          })(),
                        }}
                      >
                        {matrixColumns.map((column, colIndex) => (
                          <StyledTableCell
                            key={column.id}
                            component={colIndex === 0 ? "th" : undefined}
                            scope={colIndex === 0 ? "row" : undefined}
                            onClick={() => {
                              if (column.id === "expand" && matrixProduct.type === "MATRIX" && showChildrenAlso) {
                                handleRowExpand(matrixProduct.productID);
                              }
                            }}
                            style={{
                              borderLeft: column.id === "shopifyProductId" ? "3px solid #8c8c8c" : "none"
                            }}
                          >
                            {column.id === "expand" && matrixProduct.type === "MATRIX" && showChildrenAlso && hasVariants ? (
                              <IconButton size="small">
                                {isExpanded ? <ExpandLess /> : <ExpandMore />}
                              </IconButton>
                            ) : (
                              renderCellContent(matrixProduct, column, false, setCopySnackbar)
                            )}
                          </StyledTableCell>
                        ))}
                      </ParentRow>

                      {/* Variants Section - only show if showChildrenAlso is true and expanded */}
                      {showChildrenAlso && isExpanded && hasVariants && (
                        <>
                          {/* Variants Header Row */}
                          {renderVariantsHeaderRow()}

                          {/* Variant Rows */}
                          {matrixProduct.erply_product_variants.map((variant, variantIndex) => (
                            <ChildRow
                              key={`variant-${variant.productID}-${variantIndex}`}
                              sx={{
                                backgroundColor: (() => {
                                  // Check for validation mismatches first (highest priority)
                                  if (validationTypes.length > 0) {
                                    const hasAnyMismatch = validationTypes.some(type =>
                                      hasValidationMismatch(variant, true, type)
                                    );
                                    if (hasAnyMismatch) {
                                      return "#fff3cd !important"; // Warning yellow background
                                    }
                                  }

                                  // Then check for mapping status
                                  const isMapped = isRowMapped(variant, true);
                                  if (isMapped) {
                                    return "#e8f5e8 !important"; // Success green background
                                  }

                                  return undefined; // Default background
                                })(),
                              }}
                            >
                              {variantColumns.map((column) => (
                                <StyledTableCell
                                  key={column.id}
                                  style={{
                                    borderLeft: column.id === "shopifySku" ? "3px solid #8c8c8c" : "none"
                                  }}
                                >
                                  {column.id === "expand" ? (
                                    // Empty cell for expand column in variants
                                    null
                                  ) : (
                                    renderCellContent(variant, column, true, setCopySnackbar)
                                  )}
                                </StyledTableCell>
                              ))}
                            </ChildRow>
                          ))}
                        </>
                      )}
                    </React.Fragment>
                  );
                })
              ) : (
                <NoDataContainer>
                  <NoDataCell colSpan={matrixColumns.length}>
                    No products found
                  </NoDataCell>
                </NoDataContainer>
              )}
            </TableBody>
          )}
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[20, 50, 70, 100]}
                rowsPerPage={rowsPerPage}
                page={page}
                count={total}
                slotProps={{ select: { native: true } }}
                labelDisplayedRows={() =>
                  `${fromTable || 0} - ${toTable || 0} of ${total}`
                }
                onPageChange={onChangePage}
                onRowsPerPageChange={onChangeRowsPerPage}
                ActionsComponent={TablePaginationActions}
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>

      {/* Copy Snackbar */}
      <Snackbar
        open={copySnackbar.open}
        autoHideDuration={3000}
        onClose={handleCopySnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert onClose={handleCopySnackbarClose} severity={copySnackbar.severity}>
          {copySnackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

MatrixProductTable.propTypes = {
  matrixColumns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  variantColumns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
    })
  ).isRequired,
  rows: PropTypes.array.isRequired,
  loading: PropTypes.bool,
  sortable: PropTypes.bool,
  currentColumn: PropTypes.string,
  direction: PropTypes.bool,
  page: PropTypes.number,
  total: PropTypes.number,
  fromTable: PropTypes.number,
  toTable: PropTypes.number,
  rowsPerPage: PropTypes.number,
  onSort: PropTypes.func,
  onChangePage: PropTypes.func,
  onChangeRowsPerPage: PropTypes.func,
  expandedRows: PropTypes.instanceOf(Set),
  setExpandedRows: PropTypes.func,
  showChildrenAlso: PropTypes.bool,
  validationTypes: PropTypes.arrayOf(PropTypes.oneOf(['sku', 'barcode', 'price'])),
};

export default MatrixProductTable;
